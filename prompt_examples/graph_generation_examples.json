{"input_prefix": "INPUT: ", "output_prefix": "OUTPUT:", "examples": [{"input": [{"event": "<PERSON> discovers that his wife <PERSON> is pregnant. And they celebrate with ice cream.", "type": "personal relationships", "sub-events": ["<PERSON> discovers that his wife <PERSON> is pregnant. And they celebrate with ice cream."]}, {"event": "<PERSON> moved to a new place and become friend with <PERSON> and <PERSON>", "type": "personal relationships", "sub-events": ["<PERSON> moves to a new city.", "<PERSON> meets his neighbors <PERSON> and <PERSON> for the first time.", "<PERSON> invites <PERSON> and <PERSON> for a home-cooked meal to their new home.", "<PERSON> and <PERSON> drop by for a friendly chat."]}, {"event": "<PERSON> buys a new long surfboard and learns how to use long surfboard as well, since he only used short board in the past.", "type": "personal experiences", "sub-events": ["<PERSON> buys a new long surfboard.", "<PERSON> starts learning how to use the long surfboard.", "<PERSON> practices using the long surfboard instead of the short board he used in the past."]}, {"event": "<PERSON> has learns how to build Ikea furnitures", "type": "personal experiences", "sub-events": ["<PERSON> buys a new bed from Ikea after moving to a new city.", "<PERSON> and his wife set up the Ikea bed.", "<PERSON> buys an Ikea table and couch and sets it up over the weekend."]}, {"event": "<PERSON> is planning to organize a BBQ with his friends, and he is looking at sous vide recipes.", "type": "future social activity", "sub-events": ["<PERSON> plans to invite friends and host a BBQ party in their new home's backyard.", "<PERSON> decides to make sous vide recipes but he has no idea on how to make it.", "<PERSON> starts looking at sous vide recipes for the BBQ party.", "<PERSON> buys kitchen tools for helping him make sous vide recipes."]}, {"event": "<PERSON> is planning to build a fence for his patio, and he is asking <PERSON> who has experience with wood work.", "type": "future social activity", "sub-events": ["<PERSON> plans to build a fence for his patio but does not have experience with wood work.", "<PERSON> asks <PERSON> for suggestions who then provides a list of tips and shows <PERSON> how to do some basic woodwork.", "<PERSON> goes out to buy the necessary equipment for building the fence."]}, {"event": "<PERSON> is planning to learn how to cook Chinese food and planning to look at Chinese cooking videos on Youtube.", "type": "future non-social activity", "sub-events": ["<PERSON> eats a delicious dish in a Chinese restaurant.", "<PERSON> decides to try make the Chinese dish at home and plans to learn how to cook Chinese food.", "<PERSON> starts looking at Chinese cooking videos on Youtube.", "<PERSON> makes the Chinese dish at home and has moderate success."]}, {"event": "<PERSON> is planning to go climbing in a nearby gym when the weather will be cooler, as climbing is better with cool weather.", "type": "future non-social activity", "sub-events": ["<PERSON> wants to go rock-climbing in a nearby gym, but its too hot today so he does not.", "It is still hot today and <PERSON> does not feel like going to the gym for climbing.", "It is a cool day and <PERSON> decides it is the perfect weather to go climbing in the gym."]}, {"event": "<PERSON> goes surfing in Malibu and the waves are big. He injures his ribs hitting the surf board.", "type": "sports/leisure", "sub-events": ["<PERSON> goes surfing in Malibu and the waves are big. He injures his ribs hitting the surf board."]}, {"event": "<PERSON> went to Huntington Beach with his friends, including <PERSON>'s dog <PERSON>.", "type": "sports/leisure", "sub-events": ["<PERSON> asks <PERSON> and <PERSON> if they are free to go to the beach during the weekend. They say yes.", "<PERSON>, his wife, <PERSON>, <PERSON> and <PERSON>'s dog <PERSON>, drive to Huntington Beach on a Saturday morning."]}, {"event": "<PERSON> gets promoted at work and celebrates with his friends.", "type": "work/academic", "sub-events": ["<PERSON> is informed that he is promoted at work.", "<PERSON> plans a big celebration with this friends.", "<PERSON> and his friends go out to celebrate his promotion."]}, {"event": "<PERSON> has started to work with amazing interns, they are working hard and they are fun.", "type": "work/academic", "sub-events": ["Several interns start working with <PERSON>.", "The interns are working hard and are doing well in their projects.", "The interns try to have more team activities at the workplace and make it fun for others."]}], "output": [{"sub-event": "<PERSON> moves to a new city.", "date": "10 January, 2020", "id": "E1", "caused_by": []}, {"sub-event": "<PERSON> buys a new bed from Ikea after moving to a new city.", "date": "13 January, 2020", "id": "E2", "caused_by": ["E1"]}, {"sub-event": "<PERSON> and his wife set up the Ikea bed.", "date": "14 January, 2020", "id": "E3", "caused_by": []}, {"sub-event": "<PERSON> meets his neighbors <PERSON> and <PERSON> for the first time.", "date": "14 January, 2020", "id": "E4", "caused_by": ["E1"]}, {"sub-event": "<PERSON> discovers that his wife <PERSON> is pregnant. And they celebrate with ice cream.", "date": "21 January, 2020", "id": "E5", "caused_by": []}, {"sub-event": "<PERSON> buys an Ikea table and couch and sets it up over the weekend.", "date": "23 January, 2020", "id": "E6", "caused_by": ["E1"]}, {"sub-event": "<PERSON> invites <PERSON> and <PERSON> for a home-cooked meal to their new home.", "date": "25 January, 2020", "id": "E7", "caused_by": ["E4", "E6"]}, {"sub-event": "<PERSON> eats a delicious dish in a Chinese restaurant.", "date": "5 February, 2020", "id": "E8", "caused_by": []}, {"sub-event": "<PERSON> buys a new long surfboard.", "date": "7 February, 2020", "id": "E9", "caused_by": []}, {"sub-event": "<PERSON> and <PERSON> drop by for a friendly chat.", "date": "8 February, 2020", "id": "E10", "caused_by": ["E7"]}, {"sub-event": "<PERSON> plans to build a fence for his patio but does not have experience with wood work.", "date": "10 February, 2020", "id": "E11", "caused_by": ["E1"]}, {"sub-event": "<PERSON> starts learning how to use the long surfboard.", "date": "12 February, 2020", "id": "E12", "caused_by": ["E9"]}, {"sub-event": "<PERSON> asks <PERSON> for suggestions who then provides a list of tips and shows <PERSON> how to do some basic woodwork.", "date": "15 February, 2020", "id": "E13", "caused_by": ["E1", "E11"]}, {"sub-event": "<PERSON> decides to try make the Chinese dish at home and plans to learn how to cook Chinese food.", "date": "20 February, 2020", "id": "E14", "caused_by": ["E8"]}, {"sub-event": "<PERSON> starts looking at Chinese cooking videos on Youtube.", "date": "22 February, 2020", "id": "E15", "caused_by": ["E8", "E14"]}, {"sub-event": "<PERSON> makes the Chinese dish at home and has moderate success.", "date": "25 February, 2020", "id": "E16", "caused_by": ["E8", "E14", "E15"]}, {"sub-event": "<PERSON> goes out to buy the necessary equipment for building the fence.", "date": "28 February, 2020", "id": "E17", "caused_by": ["E11", "E13"]}, {"sub-event": "<PERSON> practices using the long surfboard instead of the short board he used in the past.", "date": "5 March, 2020", "id": "E18", "caused_by": ["E9", "E12"]}, {"sub-event": "<PERSON> plans to invite friends and host a BBQ party in their new home's backyard.", "date": "10 March, 2020", "id": "E19", "caused_by": []}, {"sub-event": "<PERSON> goes surfing in Malibu and the waves are big. He injures his ribs hitting the surf board.", "date": "14 March, 2020", "id": "E20", "caused_by": ["E18"]}, {"sub-event": "<PERSON> decides to make sous vide recipes but he has no idea on how to make it.", "date": "19 March, 2020", "id": "E21", "caused_by": ["E19"]}, {"sub-event": "<PERSON> asks <PERSON> and <PERSON> if they are free to go to the beach during the weekend. They say yes.", "date": "23 March, 2020", "id": "E22", "caused_by": ["E10"]}, {"sub-event": "<PERSON> starts looking at sous vide recipes for the BBQ party.", "date": "25 March, 2020", "id": "E23", "caused_by": ["E19", "E21"]}, {"sub-event": "<PERSON>, his wife, <PERSON>, <PERSON> and <PERSON>'s dog <PERSON>, drive to Huntington Beach on a Saturday morning.", "date": "27 March, 2020", "id": "E24", "caused_by": ["E10", "E22"]}, {"sub-event": "<PERSON> is informed that he is promoted at work.", "date": "1 April, 2020", "id": "E25", "caused_by": []}, {"sub-event": "Several interns start working with <PERSON>.", "date": "5 April, 2020", "id": "E26", "caused_by": []}, {"sub-event": "<PERSON> plans a big celebration with this friends.", "date": "6 April, 2020", "id": "E27", "caused_by": ["E25"]}, {"sub-event": "<PERSON> wants to go rock-climbing in a nearby gym, but its too hot today so he does not.", "date": "7 April, 2020", "id": "E28", "caused_by": []}, {"sub-event": "<PERSON> buys kitchen tools for helping him make sous vide recipes.", "date": "9 April, 2020", "id": "E29", "caused_by": ["E19", "E21", "E23"]}, {"sub-event": "<PERSON> and his friends go out to celebrate his promotion.", "date": "13 April, 2020", "id": "E30", "caused_by": ["E25", "E27"]}, {"sub-event": "It is still hot today and <PERSON> does not feel like going to the gym for climbing.", "date": "15 April, 2020", "id": "E31", "caused_by": ["E28"]}, {"sub-event": "The interns are working hard and are doing well in their projects.", "date": "13 April, 2020", "id": "E32", "caused_by": ["E26"]}, {"sub-event": "It is a cool day and <PERSON> decides it is the perfect weather to go climbing in the gym.", "date": "18 April, 2020", "id": "E33", "caused_by": ["E28"]}, {"sub-event": "The interns try to have more team activities at the workplace and make it fun for others.", "date": "18 April, 2020", "id": "E34", "caused_by": ["E26", "E32"]}]}]}