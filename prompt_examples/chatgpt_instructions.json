{"user_session_1": "Here is a json file containing a conversation between two speakers, Speaker 1 and Speaker 2. Their personalities are saved under the key 'personas'. The information under 'session_1' contains the turn-wise conversation between them and builds on the information contained under 'personas'. After the 'session_1' conversation is over, the personas are updated with the new facts discussed in the conversation and saved under 'session_1_end_summary'. Then, a time indicated under 'session_2_start_time_lapse' passes after the conversation.", "user_session_2": "After the time indicated under 'session_2_start_time_lapse', the next conversation takes place which is saved under 'session_2' in the following json file. It builds on the updated persona summary saved under 'session_1_end_summary'. As you can see, newer facts are added to the personality summary of each speaker at the end of the conversation. The conversation maintains continuity from previous conversations, just like the conversations people have in the real-world. Again, as indicated under the key 'session_3_start_time_lapse', sometime passes after the conversation in 'session_2'.", "user_session_3": "The next json file contains the conversation that happens in 'session_3' and the updated personas are now saved in 'session_3_end_summary'.", "user_query": "Pick a random time between 1 hour and 7 days for the time to lapse after the conversation in 'session_3'. Generate a json file that contains a hypothetical conversation that might take place between 'Speaker 1' and 'Speaker 2' based on the conversations in 'session_1', 'session_2' and 'session_3', and save it under 'session_4'. Save the time lapsed under 'session_4_start_time_lapse'. Maintain the dialog style of each speaker such as short sentences, casual wording etc. and build on the facts discussed in the previous conversations. Add new facts about their lives to the conversation, suited to their respective personalities. Keep the conversation casual and the sentences short. Include hypothetical references to time such as 'last Friday', 'next month' or 'when I was ten years old'. Include hypothetical references to specific places and locations. Make the conversation personal e.g., have the speakers talk about their family or friends, aspirations and feelings.", "user_session_k": "After the time indicated under 'session_2_start_time_lapse', the next conversation takes place and builds on the updated persona summary saved under 'session_1_end_summary'. Newer facts are added to the personality summary of each speaker at the end of the conversation and saved as 'session_2_end_summary' in the following json file. The conversation maintains continuity from previous conversations, just like the conversations people have in the real-world. Similarly, follow-up conversations take place and the updated personalities are saved under 'session_3_end_summary', 'session_4_end_summary'", "user_query_k": "Pick a random time between 1 hour and 7 days for the time to lapse after the last conversation. Generate a json file that contains a hypothetical conversation that might take place between 'Speaker 1' and 'Speaker 2' based on the conversation in 'session_1', personality summaries from all previous rounds of conversation, and save it under 'session_%s'. It must contain the fields 'session_%s_start_time_lapse', 'session_%s' and 'session_%s_end_summary' only. Save the time lapsed under 'session_%s_start_time_lapse'. Maintain the dialog style of each speaker such as short sentences, casual wording etc. as seen in 'session_1' and build on the facts discussed in the previous conversations, as seen in the summary fields. Do not reintroduce facts already present in the summaries. Add new facts about their lives to the conversation, suited to their respective personalities and add follow-up conversation about previously introduced facts. Keep the conversation casual and the sentences short. Include hypothetical references to time such as 'last Friday', 'next month' or 'when I was ten years old'. Include hypothetical references to specific places and locations. Make the conversation personal e.g., have the speakers talk about their family or friends, aspirations and feelings. Save the updated personalities under 'session_%s_end_summary'.", "summary_session": "Here is a json file containing a conversation between two speakers, Speaker 1 and Speaker 2. Their personalities are saved under the key 'personas'. The information under 'session' contains the turn-wise conversation between them and builds on the information contained under 'personas'. All new information gained about Speaker 1 and Speaker 2 in the conversation are added to the information under 'personas' and saved to 'session_end_summary'.", "summary_query": "Here is another json file containing the next conversation between Speaker 1 and Speaker 2 in 'new_session'. Update the information contained in 'session_1_end_summary' with new information from 'new_session' and save it in a json object under the field 'new_session_end_summary'. Add all of the new facts that have come up during the conversation in 'new_session' to the updated personas and also update existing facts based on new information wherever needed.", "probing_question": "Let's write questions from facts about a person. For example,\n\nFact: I went to a new vegan restaurant and had an amazing tofu dish.\nQuestion: What did <PERSON> eat at the vegan restaurant?\nAnswer: Tofu.\n\nNote: The questions can be about time, place, sentiment, feelings or entities.\n\n For the following facts, write questions and answers in a similar manner. Output a json file with keys 'fact', 'question', 'answer'.", "question_abstract": "", "info_abstract": "", "summarize_instruction_v0": "Let's write summaries about speakers from a conversation. The input consists of a json dictionary object that contains a list of facts about two speakers and a conversation takes place between the two speakers along with the date stamp. The output is a json dictionary that contains new facts about the speakers that have emerged from the conversation and are not present in facts under 'personas'. For example, \n\nInput:\n\n%s\n\nDate: %s\n\n%s\n\nOutput:\n\n%s\n\nFor the personas and the conversation given below, write the summary.\n\nInput:\n\n%s\n\nDate: %s\n\n%s\n\nOutput:\n\n", "summarize_instruction": "Let's write summaries about speakers from a conversation. The input is a conversation takes place between the two speakers along with the date stamp. The output is a json dictionary that contains facts about the speakers that have emerged from the conversation. For example, \n\nInput:\n\nDate: %s\n\n%s\n\nOutput:\n\n%s\n\nFor the personas and the conversation given below, write the summary.\n\nInput:\n\nDate: %s\n\n%s\n\nOutput:\n\n", "rewrite_mm": "Here is a conversation between two speakers.\n\n%s\n\nRewrite this conversation in the form of a json file where Speaker 1 references to images having the following captions at some point in the conversation, with follow-up questions from Speaker 2 based on the images. When referencing the image in the conversation, use the associated id in brackets. \n\n%s\n\nOutput:\n\n", "probing_questions": "Here is a conversation between two speakers. After each response, write one or more questions that can be answered by the information mentioned in the particular response and any relevant information in the conversation so far. For example, ", "agent_1_instruction": "Name: %s\n %s has the following characteristics: %s\n %s is having a conversation with %s. Here is the dialogue history:\n%s\nHow would %s respond?", "agent_2_instruction": "Name: %s\n %s has the following characteristics: %s\n %s is having a conversation with %s. Here is the dialogue history:\n%s\nHow would %s respond?"}