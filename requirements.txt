# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=main
_openmp_mutex=5.1=1_gnu
accelerate=0.24.1=pypi_0
aiofiles=23.2.1=pypi_0
aiohttp=3.8.4=pypi_0
aiosignal=1.3.1=pypi_0
altair=5.1.2=pypi_0
anthropic=0.32.0=pypi_0
antlr4-python3-runtime=4.9.3=pypi_0
anyio=3.7.1=pypi_0
appdirs=1.4.4=pypi_0
argon2-cffi=23.1.0=pypi_0
argon2-cffi-bindings=21.2.0=pypi_0
arrow=1.3.0=pypi_0
asttokens=2.4.1=pypi_0
async-lru=2.0.4=pypi_0
async-timeout=4.0.2=pypi_0
attrs=22.2.0=pypi_0
babel=2.13.1=pypi_0
beautifulsoup4=4.12.2=pypi_0
bert-score=0.3.13=pypi_0
blas=1.0=mkl
bleach=6.1.0=pypi_0
blis=0.7.11=pypi_0
braceexpand=0.1.7=pypi_0
brotli-python=1.0.9=py39h6a678d5_7
bzip2=1.0.8=h7b6447c_0
ca-certificates=2023.08.22=h06a4308_0
cachetools=5.4.0=pypi_0
catalogue=2.0.10=pypi_0
cchardet=2.1.7=pypi_0
certifi=2023.7.22=py39h06a4308_0
cffi=1.15.1=py39h5eee18b_3
chardet=5.1.0=pypi_0
charset-normalizer=2.0.4=pyhd3eb1b0_0
click=8.1.7=pypi_0
comm=0.1.4=pypi_0
confection=0.1.3=pypi_0
contourpy=1.0.7=pypi_0
cryptography=41.0.3=py39hdda0065_0
cuda-cudart=11.7.99=0
cuda-cupti=11.7.101=0
cuda-libraries=11.7.1=0
cuda-nvrtc=11.7.99=0
cuda-nvtx=11.7.91=0
cuda-runtime=11.7.1=0
cycler=0.11.0=pypi_0
cymem=2.0.8=pypi_0
debugpy=1.8.0=pypi_0
decorator=5.1.1=pypi_0
decord=0.6.0=pypi_0
defusedxml=0.7.1=pypi_0
diffusers=0.21.4=pypi_0
distro=1.8.0=pypi_0
docker-pycreds=0.4.0=pypi_0
et-xmlfile=1.1.0=pypi_0
exceptiongroup=1.1.3=pypi_0
executing=2.0.1=pypi_0
fastapi=0.104.1=pypi_0
fastjsonschema=2.18.1=pypi_0
ffmpeg=4.3=hf484d3e_0
ffmpy=0.3.1=pypi_0
filelock=3.9.0=py39h06a4308_0
fonttools=4.38.0=pypi_0
fqdn=1.5.1=pypi_0
freetype=2.12.1=h4a9f257_0
frozenlist=1.3.3=pypi_0
fsspec=2023.10.0=pypi_0
ftfy=6.1.1=pypi_0
gdown=4.7.1=pypi_0
giflib=5.2.1=h5eee18b_3
gitdb=4.0.11=pypi_0
gitpython=3.1.40=pypi_0
gmp=6.2.1=h295c915_3
gmpy2=2.1.2=py39heeb90bb_0
gnutls=3.6.15=he1e5248_0
google-ai-generativelanguage=0.6.6=pypi_0
google-api-core=2.19.1=pypi_0
google-api-python-client=2.140.0=pypi_0
google-auth=2.33.0=pypi_0
google-auth-httplib2=0.2.0=pypi_0
google-generativeai=0.7.2=pypi_0
googleapis-common-protos=1.63.2=pypi_0
gradio=3.24.1=pypi_0
gradio-client=0.0.8=pypi_0
grpcio=1.65.4=pypi_0
grpcio-status=1.62.3=pypi_0
h11=0.14.0=pypi_0
httpcore=0.18.0=pypi_0
httplib2=0.22.0=pypi_0
httpx=0.25.0=pypi_0
huggingface-hub=0.17.3=pypi_0
idna=3.4=py39h06a4308_0
importlib-metadata=6.8.0=pypi_0
importlib-resources=5.12.0=pypi_0
intel-openmp=2023.1.0=hdb19cb5_46305
iopath=0.1.10=pypi_0
ipykernel=6.26.0=pypi_0
ipython=8.17.2=pypi_0
isoduration=20.11.0=pypi_0
jedi=0.19.1=pypi_0
jinja2=3.1.2=py39h06a4308_0
jiter=0.5.0=pypi_0
joblib=1.3.2=pypi_0
jpeg=9e=h5eee18b_1
json5=0.9.14=pypi_0
jsonpointer=2.4=pypi_0
jsonschema=4.19.2=pypi_0
jsonschema-specifications=2023.7.1=pypi_0
jupyter-client=8.5.0=pypi_0
jupyter-core=5.5.0=pypi_0
jupyter-events=0.8.0=pypi_0
jupyter-lsp=2.2.0=pypi_0
jupyter-server=2.9.1=pypi_0
jupyter-server-terminals=0.4.4=pypi_0
jupyterlab=4.0.7=pypi_0
jupyterlab-pygments=0.2.2=pypi_0
jupyterlab-server=2.25.0=pypi_0
kiwisolver=1.4.4=pypi_0
lame=3.100=h7b6447c_0
langcodes=3.3.0=pypi_0
lcms2=2.12=h3be6417_0
ld_impl_linux-64=2.38=h1181459_1
lerc=3.0=h295c915_0
libcublas=**********=0
libcufft=**********=h4fbf590_0
libcufile=********=0
libcurand=*********=0
libcusolver=********=0
libcusparse=*********=0
libdeflate=1.17=h5eee18b_1
libffi=3.4.4=h6a678d5_0
libgcc-ng=11.2.0=h1234567_1
libgomp=11.2.0=h1234567_1
libiconv=1.16=h7f8727e_2
libidn2=2.3.4=h5eee18b_0
libnpp=*********=0
libnvjpeg=11.8.0.2=0
libpng=1.6.39=h5eee18b_0
libstdcxx-ng=11.2.0=h1234567_1
libtasn1=4.19.0=h5eee18b_0
libtiff=4.5.1=h6a678d5_0
libunistring=0.9.10=h27cfd23_0
libwebp=1.3.2=h11a3e52_0
libwebp-base=1.3.2=h5eee18b_0
lightning=2.1.0=pypi_0
lightning-utilities=0.9.0=pypi_0
linkify-it-py=2.0.2=pypi_0
llvmlite=0.41.1=pypi_0
lz4-c=1.9.4=h6a678d5_0
markdown-it-py=2.2.0=pypi_0
markupsafe=2.1.1=py39h7f8727e_0
matplotlib=3.7.0=pypi_0
matplotlib-inline=0.1.6=pypi_0
mdit-py-plugins=0.3.3=pypi_0
mdurl=0.1.2=pypi_0
mistune=3.0.2=pypi_0
mkl=2023.1.0=h213fc3f_46343
mkl-service=2.4.0=py39h5eee18b_1
mkl_fft=1.3.8=py39h5eee18b_0
mkl_random=1.2.4=py39hdb19cb5_0
mpc=1.1.0=h10f8cd9_1
mpfr=4.0.2=hb69a4c5_1
mpmath=1.3.0=py39h06a4308_0
multidict=6.0.4=pypi_0
murmurhash=1.0.10=pypi_0
nbclient=0.8.0=pypi_0
nbconvert=7.10.0=pypi_0
nbformat=5.9.2=pypi_0
ncurses=6.4=h6a678d5_0
nest-asyncio=1.5.8=pypi_0
nettle=3.7.3=hbbd107a_1
networkx=3.1=py39h06a4308_0
nltk=3.8.1=pypi_0
notebook=7.0.6=pypi_0
notebook-shim=0.2.3=pypi_0
numba=0.58.1=pypi_0
numpy=1.26.0=py39h5f9d8c6_0
numpy-base=1.26.0=py39hb5e798b_0
omegaconf=2.3.0=pypi_0
open-clip-torch=2.23.0=pypi_0
openai=0.28.0=pypi_0
opencv-python=********=pypi_0
openh264=2.1.1=h4ff587b_0
openjpeg=2.4.0=h3ad879b_0
openpyxl=3.1.2=pypi_0
openssl=3.0.11=h7f8727e_2
orjson=3.9.10=pypi_0
overrides=7.4.0=pypi_0
packaging=23.0=pypi_0
pandas=2.1.2=pypi_0
pandocfilters=1.5.0=pypi_0
parso=0.8.3=pypi_0
pathtools=0.1.2=pypi_0
pathy=0.10.3=pypi_0
peft=0.5.0=pypi_0
pexpect=4.8.0=pypi_0
pillow=10.0.1=py39ha6cbd5a_0
pip=23.3=py39h06a4308_0
platformdirs=3.11.0=pypi_0
portalocker=2.8.2=pypi_0
preshed=3.0.9=pypi_0
prometheus-client=0.18.0=pypi_0
prompt-toolkit=3.0.39=pypi_0
proto-plus=1.24.0=pypi_0
protobuf=4.25.0=pypi_0
psutil=5.9.4=pypi_0
ptyprocess=0.7.0=pypi_0
pure-eval=0.2.2=pypi_0
pyasn1=0.6.0=pypi_0
pyasn1-modules=0.4.0=pypi_0
pycocoevalcap=1.2=pypi_0
pycocotools=2.0.6=pypi_0
pycparser=2.21=pyhd3eb1b0_0
pydantic=1.10.13=pypi_0
pydub=0.25.1=pypi_0
pygments=2.16.1=pypi_0
pynndescent=0.5.10=pypi_0
pyopenssl=23.2.0=py39h06a4308_0
pyparsing=3.0.9=pypi_0
pysocks=1.7.1=py39h06a4308_0
python=3.9.18=h955ad1f_0
python-dateutil=2.8.2=pypi_0
python-json-logger=2.0.7=pypi_0
python-multipart=0.0.6=pypi_0
pytorch=2.0.1=py3.9_cuda11.7_cudnn8.5.0_0
pytorch-cuda=11.7=h778d358_5
pytorch-fid=0.3.0=pypi_0
pytorch-lightning=2.1.0=pypi_0
pytorch-mutex=1.0=cuda
pytz=2023.3.post1=pypi_0
pyyaml=6.0=pypi_0
pyzmq=25.1.1=pypi_0
readline=8.2=h5eee18b_0
referencing=0.30.2=pypi_0
regex=2022.10.31=pypi_0
requests=2.31.0=py39h06a4308_0
rfc3339-validator=0.1.4=pypi_0
rfc3986-validator=0.1.1=pypi_0
rouge=1.0.1=pypi_0
rpds-py=0.10.6=pypi_0
rsa=4.9=pypi_0
safetensors=0.4.0=pypi_0
scikit-learn=1.3.2=pypi_0
scipy=1.11.3=pypi_0
seaborn=0.13.0=pypi_0
semantic-version=2.10.0=pypi_0
send2trash=1.8.2=pypi_0
sentence-transformers=2.2.2=pypi_0
sentencepiece=0.1.99=pypi_0
sentry-sdk=1.34.0=pypi_0
setproctitle=1.3.3=pypi_0
setuptools=68.0.0=py39h06a4308_0
six=1.16.0=pypi_0
smart-open=6.4.0=pypi_0
smmap=5.0.1=pypi_0
sniffio=1.3.0=pypi_0
soupsieve=2.5=pypi_0
spacy=3.5.1=pypi_0
spacy-legacy=3.0.12=pypi_0
spacy-loggers=1.0.5=pypi_0
sqlite=3.41.2=h5eee18b_0
srsly=2.4.8=pypi_0
stack-data=0.6.3=pypi_0
starlette=0.27.0=pypi_0
sympy=1.11.1=py39h06a4308_0
tbb=2021.10.0=pypi_0
tenacity=8.2.2=pypi_0
terminado=0.17.1=pypi_0
thinc=8.1.12=pypi_0
threadpoolctl=3.2.0=pypi_0
tiktoken=0.5.2=pypi_0
timm=0.6.13=pypi_0
tinycss2=1.2.1=pypi_0
tk=8.6.12=h1ccaba5_0
tokenizers=0.14.1=pypi_0
tomli=2.0.1=pypi_0
toolz=0.12.0=pypi_0
torch-fidelity=0.3.0=pypi_0
torchaudio=2.0.2=py39_cu117
torchmetrics=1.2.0=pypi_0
torchtriton=2.0.0=py39
torchvision=0.15.2=py39_cu117
tornado=6.3.3=pypi_0
tqdm=4.64.1=pypi_0
traitlets=5.13.0=pypi_0
transformers=4.35.0=pypi_0
typer=0.7.0=pypi_0
types-python-dateutil=2.8.19.14=pypi_0
typing-extensions=4.8.0=pypi_0
tzdata=2023.3=pypi_0
uc-micro-py=1.0.2=pypi_0
umap-learn=0.5.4=pypi_0
uri-template=1.3.0=pypi_0
uritemplate=4.1.1=pypi_0
urllib3=1.26.18=py39h06a4308_0
uvicorn=0.23.2=pypi_0
wandb=0.15.12=pypi_0
wasabi=1.1.2=pypi_0
wcwidth=0.2.9=pypi_0
webcolors=1.13=pypi_0
webdataset=0.2.48=pypi_0
webencodings=0.5.1=pypi_0
websocket-client=1.6.4=pypi_0
websockets=12.0=pypi_0
wheel=0.41.2=py39h06a4308_0
wordcloud=1.9.3=pypi_0
xformers=0.0.22.post7=py39_cu11.8.0_pyt2.0.1
xz=5.4.2=h5eee18b_0
yarl=1.8.2=pypi_0
zipp=3.14.0=pypi_0
zlib=1.2.13=h5eee18b_0
zstd=1.5.5=hc292b87_0
